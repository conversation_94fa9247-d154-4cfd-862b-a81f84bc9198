'use client'

import { Suspense, lazy, useState } from 'react'
import { useInView } from 'react-intersection-observer' // Import useInView

const Spline = lazy(() =>
  import('@splinetool/react-spline').catch(error => {
    console.error('Failed to load Spline component:', error);
    // Return a fallback component
    return { default: () => null };
  })
)

interface SplineSceneProps {
  scene: string
  className?: string
}

export function SplineScene({ scene, className }: SplineSceneProps) {
  const { ref, inView } = useInView({
    triggerOnce: true, // Only trigger once when it enters the viewport
    threshold: 0.1, // Trigger when 10% of the element is visible
  })

  const [hasError, setHasError] = useState(false)
  const [isLoaded, setIsLoaded] = useState(false)

  // Define the loader component separately for clarity
  const Loader = () => (
    <div className="w-full h-full flex items-center justify-center">
      {/* Basic CSS Loader */}
      <style jsx>{`
        .loader {
          width: 48px;
          height: 48px;
          border: 5px solid #FFF; /* White border */
          border-bottom-color: transparent;
          border-radius: 50%;
          display: inline-block;
          box-sizing: border-box;
          animation: rotation 1s linear infinite;
        }

        @keyframes rotation {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }
      `}</style>
      <span className="loader"></span>
    </div>
  );

  // Error fallback component
  const ErrorFallback = () => (
    <div className="w-full h-full flex items-center justify-center bg-purple-900/20 rounded-lg">
      <div className="text-center p-4">
        <div className="text-purple-300 mb-2">
          <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <p className="text-purple-200 text-sm">3D Animation Unavailable</p>
        <p className="text-purple-300/70 text-xs mt-1">Loading fallback...</p>
      </div>
    </div>
  );

  return (
    // Attach the ref to the container div
    <div ref={ref} className={className} style={{ position: 'relative' }}>
      <style jsx>{`
        /* Reduce Spline canvas resolution by scaling down */
        canvas {
          width: 100% !important;
          height: 100% !important;
          transform: scale(0.75); /* Adjust scale factor as needed */
          transform-origin: top left;
          image-rendering: pixelated;
        }
      `}</style>
      {/* Conditionally render the Spline component only when inView is true */}
      {hasError ? (
        <ErrorFallback />
      ) : inView ? (
        <Suspense fallback={<Loader />}>
          <Spline
            scene={scene}
            onLoad={() => {
              setIsLoaded(true);
              console.log('Spline scene loaded successfully');
            }}
            onError={(error: any) => {
              console.error('Spline loading error:', error);
              setHasError(true);
            }}
          />
        </Suspense>
      ) : (
        // Optionally, render the loader or nothing while not in view
        <Loader />
      )}
    </div>
  )
}
