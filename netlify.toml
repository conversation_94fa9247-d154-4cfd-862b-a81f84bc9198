[build]
  command = "npm run build"

# Use Netlify's Next.js plugin for full Next.js support
[[plugins]]
  package = "@netlify/plugin-nextjs"

# Headers for CORS and security
[[headers]]
  for = "/api/*"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Methods = "GET, POST, PUT, DELETE, OPTIONS"
    Access-Control-Allow-Headers = "Content-Type, Authorization"

[[headers]]
  for = "/company_knowledge.json"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Cache-Control = "public, max-age=3600"

# Security headers with CSP to block tracking scripts
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "SAMEORIGIN"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://assets.calendly.com https://calendly.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://assets.calendly.com; font-src 'self' https://fonts.gstatic.com https://assets.calendly.com; img-src 'self' data: https: blob:; connect-src 'self' https://api.calendly.com https://calendly.com; frame-src 'self' https://calendly.com; object-src 'none'; base-uri 'self'"



# Environment variables (these will be set in Netlify dashboard)
# MAILGUN_API_KEY
# MAILGUN_DOMAIN
# MAILGUN_FROM
# MAILGUN_REGION
# NEXT_PUBLIC_OPENAI_API_KEY
